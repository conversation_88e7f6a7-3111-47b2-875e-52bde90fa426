import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

import matplotlib.pyplot as plt
%matplotlib inline
torch.manual_seed(42)

import numpy as np
import torch
from torch.utils.data import Dataset

class N_MNIST_Data(Dataset):
  def __init__(self, image_file, label_file):
    images = np.loadtxt(image_file, delimiter=',', dtype=np.float32).reshape(-1, 1, 28, 28)
    images /= 255.0
    self.images = torch.tensor(images, dtype=torch.float32)

    one_hot = np.loadtxt(label_file, delimiter=',', dtype=np.int64)
    self.labels = torch.tensor(np.argmax(one_hot, axis=1), dtype=torch.long)

  def __len__(self):
    return len(self.labels)

  def __getitem__(self, idx):
    return self.images[idx], self.labels[idx]


import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader

class CNN(nn.Module):
  def __init__(self,
               conv_channels=[16, 32],
               kernel_sizes=[3, 3],
               strides=[1, 1],
               poolings=['max', 'max'],
               fc_sizes=[128],
               lr=0.001,
               epochs=10,
               batch_size=64):
    
    super().__init__()
    self.epochs = epochs
    self.batch_size = batch_size
    self.lr = lr

    self.conv_layers = self._make_conv_layers(conv_channels, kernel_sizes, strides, poolings)
    conv_out_size = self._get_conv_output_size()

    # Fully connected layers
    fc_layers = []
    in_features = conv_out_size
    for size in fc_sizes:
      fc_layers.append(nn.Linear(in_features, size))
      fc_layers.append(nn.ReLU())
      in_features = size
    fc_layers.append(nn.Linear(in_features, 10))  # 10 output classes
    self.fc = nn.Sequential(*fc_layers)

    self.loss_fn = nn.CrossEntropyLoss()
    self.optimizer = optim.Adam(self.parameters(), lr=lr)

  def _make_conv_layers(self, channels, kernels, strides, poolings):
    layers = []
    in_channels = 1  # grayscale
    for out_channels, k, s, pool in zip(channels, kernels, strides, poolings):
      layers.append(nn.Conv2d(in_channels, out_channels, kernel_size=k, stride=s, padding=k // 2))
      layers.append(nn.ReLU())
      if pool == 'max':
        layers.append(nn.MaxPool2d(kernel_size=2))
      elif pool == 'avg':
        layers.append(nn.AvgPool2d(kernel_size=2))
      in_channels = out_channels
    return nn.Sequential(*layers)

  def _get_conv_output_size(self):
    with torch.no_grad():
      dummy_input = torch.zeros(1, 1, 28, 28)
      out = self.conv_layers(dummy_input)
      return out.view(1, -1).shape[1]

  def forward(self, x):
    x = self.conv_layers(x)
    x = x.view(x.size(0), -1)
    return self.fc(x)

  def train_model(self, train_dataset, device='cuda'):
    self.to(device)
    loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
    for epoch in range(self.epochs):
      self.train()
      total_loss = 0
      for x, y in loader:
        x, y = x.to(device), y.to(device)
        self.optimizer.zero_grad()
        loss = self.loss_fn(self(x), y)
        loss.backward()
        self.optimizer.step()
        total_loss += loss.item()
      print(f"Epoch {epoch+1}/{self.epochs}, Loss: {total_loss/len(loader):.4f}")

  def evaluate(self, test_dataset, device='cuda'):
    self.to(device)
    self.eval()
    loader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)
    correct = 0
    total = 0
    with torch.no_grad():
      for x, y in loader:
        x, y = x.to(device), y.to(device)
        outputs = self(x)
        _, predicted = torch.max(outputs.data, 1)
        total += y.size(0)
        correct += (predicted == y).sum().item()
    
    accuracy = 100 * correct / total
    return accuracy

dataset = N_MNIST_Data('n-MNIST/trainx.csv', 'n-MNIST/trainy.csv')
test_dataset = N_MNIST_Data('n-MNIST/testx.csv', 'n-MNIST/testy.csv')

conv_channels = [[8, 16], [16, 32], [32, 64], [32, 64, 128]]
kernal_sizes = [[3, 3], [5, 5], [7, 7], [9, 9]]
strides = [[1, 1], [2, 2], [1, 2], [2, 1]]
poolings = [['max', 'max'], ['avg', 'avg'], ['max', 'avg'], ['avg', 'max']]
fc_sizes = [[64], [128], [256], [128, 64], [256, 128]]
lr = [0.1, 0.01, 0.01, 0.001]
epochs = [10, 20, 30, 40, 50]
batch_size = [16, 32, 64, 128, 256, 512]

conv_channels_models = [CNN(conv_channels=conv_channels[i]) for i in range(len(conv_channels))]
kernal_sizes_models = [CNN(kernel_sizes=kernal_sizes[i]) for i in range(len(kernal_sizes))]
strides_models = [CNN(strides=strides[i]) for i in range(len(strides))]
poolings_models = [CNN(poolings=poolings[i]) for i in range(len(poolings))]
fc_sizes_models = [CNN(fc_sizes=fc_sizes[i]) for i in range(len(fc_sizes))]

for model in conv_channels_models:
  model.train_model(dataset)

for model in kernal_sizes_models:
  model.train_model(dataset)

for model in strides_models:
  model.train_model(dataset)

for model in poolings_models:
  model.train_model(dataset)

for model in fc_sizes_models:
  model.train_model(dataset)

conv_channels_results = [model.evaluate(test_dataset) for model in conv_channels_models]
kernal_sizes_results = [model.evaluate(test_dataset) for model in kernal_sizes_models]
strides_results = [model.evaluate(test_dataset) for model in strides_models]
poolings_results = [model.evaluate(test_dataset) for model in poolings_models]
fc_sizes_results = [model.evaluate(test_dataset) for model in fc_sizes_models]


import pandas as pd
from IPython.display import display

conv_channels_df = pd.DataFrame({'conv_channels': conv_channels, 'accuracy': conv_channels_results})
kernal_sizes_df = pd.DataFrame({'kernal_sizes': kernal_sizes, 'accuracy': kernal_sizes_results})
strides_df = pd.DataFrame({'strides': strides, 'accuracy': strides_results})
poolings_df = pd.DataFrame({'poolings': poolings, 'accuracy': poolings_results})
fc_sizes_df = pd.DataFrame({'fc_sizes': fc_sizes, 'accuracy': fc_sizes_results})

display(conv_channels_df.style.hide(axis='index').format(precision=2))
display(kernal_sizes_df.style.hide(axis='index').format(precision=2))
display(strides_df.style.hide(axis='index').format(precision=2))
display(poolings_df.style.hide(axis='index').format(precision=2))
display(fc_sizes_df.style.hide(axis='index').format(precision=2))